import 'package:flutter/material.dart';
import 'file_list_page.dart';
import 'debug_info_page.dart';
import '../services/theme_service.dart';
import '../services/token_manager.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final ThemeService _themeService = ThemeService();

  @override
  void initState() {
    super.initState();
    _themeService.addListener(_onThemeChanged);
  }

  @override
  void dispose() {
    _themeService.removeListener(_onThemeChanged);
    super.dispose();
  }

  void _onThemeChanged() {
    setState(() {});
  }

  Future<void> _logout(BuildContext context) async {
    final tokenManager = TokenManager();
    await tokenManager.clearTokens();
    
    if (context.mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  void _showThemeSelector(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('选择主题'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: AppThemeMode.values.map((mode) {
              return RadioListTile<AppThemeMode>(
                title: Text(_getThemeModeName(mode)),
                subtitle: Text(_getThemeModeDescription(mode)),
                value: mode,
                groupValue: _themeService.themeMode,
                onChanged: (AppThemeMode? value) {
                  if (value != null) {
                    _themeService.setThemeMode(value);
                    Navigator.of(context).pop();
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
          ],
        );
      },
    );
  }

  String _getThemeModeName(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return '明亮模式';
      case AppThemeMode.dark:
        return '暗黑模式';
      case AppThemeMode.system:
        return '跟随系统';
    }
  }

  String _getThemeModeDescription(AppThemeMode mode) {
    switch (mode) {
      case AppThemeMode.light:
        return '始终使用明亮主题';
      case AppThemeMode.dark:
        return '始终使用暗黑主题';
      case AppThemeMode.system:
        return '跟随系统设置';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('115 文件管理'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: Icon(_themeService.themeModeIcon),
            onPressed: () => _showThemeSelector(context),
            tooltip: '主题设置 (${_themeService.themeModeName})',
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => Navigator.of(context).push(
              MaterialPageRoute(builder: (context) => const DebugInfoPage()),
            ),
            tooltip: '调试信息',
          ),
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: () => _logout(context),
            tooltip: '登出',
          ),
        ],
      ),
      body: const FileListPage(
        cid: '0', // 根目录
        title: '我的文件',
      ),
    );
  }
} 