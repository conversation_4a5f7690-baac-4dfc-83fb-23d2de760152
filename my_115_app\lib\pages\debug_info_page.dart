import 'package:flutter/material.dart';
import 'package:talker_flutter/talker_flutter.dart';
import 'package:get_it/get_it.dart';
import '../services/talker_service.dart';

class DebugInfoPage extends StatefulWidget {
  const DebugInfoPage({super.key});

  @override
  State<DebugInfoPage> createState() => _DebugInfoPageState();
}

class _DebugInfoPageState extends State<DebugInfoPage> with TickerProviderStateMixin {
  final TalkerService _talkerService = GetIt.instance<TalkerService>();
  Map<String, dynamic>? _talkerStats;
  
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadStats() {
    setState(() {
      _talkerStats = _talkerService.getStats();
    });
  }

  Color _getStatColor(String type) {
    final brightness = Theme.of(context).brightness;
    final isDark = brightness == Brightness.dark;
    
    switch (type) {
      case 'error':
        return isDark ? Colors.red[400]! : Colors.red[600]!;
      case 'warning':
        return isDark ? Colors.orange[400]! : Colors.orange[600]!;
      case 'success':
        return isDark ? Colors.green[400]! : Colors.green[600]!;
      case 'info':
        return isDark ? Colors.blue[400]! : Colors.blue[600]!;
      case 'primary':
        return isDark ? Colors.purple[400]! : Colors.purple[600]!;
      case 'secondary':
        return isDark ? Colors.teal[400]! : Colors.teal[600]!;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('调试信息'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadStats,
            tooltip: '刷新',
          ),
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: () {
              // 直接打开 Talker 的完整调试界面
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => TalkerScreen(
                    talker: _talkerService.talker,
                    theme: TalkerScreenTheme(
                      backgroundColor: Theme.of(context).colorScheme.surface,
                      textColor: Theme.of(context).colorScheme.onSurface,
                      cardColor: Theme.of(context).colorScheme.surfaceVariant,
                    ),
                  ),
                ),
              );
            },
            tooltip: 'Talker 完整日志界面',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.analytics), text: '统计'),
            Tab(icon: Icon(Icons.storage), text: '缓存'),
            Tab(icon: Icon(Icons.list), text: '快速日志'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildStatsTab(),
          _buildCacheTab(),
          _buildQuickLogsTab(),
        ],
      ),
    );
  }

  Widget _buildStatsTab() {
    if (_talkerStats == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTalkerStatsSection(),
          const SizedBox(height: 24),
          _buildQuickActionsSection(),
        ],
      ),
    );
  }

  Widget _buildTalkerStatsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Talker 日志统计',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 总体统计
            Row(
              children: [
                Expanded(
                  child: _buildStatChip('总日志数', '${_talkerStats!['totalLogs']}', _getStatColor('info')),
                ),
                Expanded(
                  child: _buildStatChip('错误数', '${_talkerStats!['errors']}', _getStatColor('error')),
                ),
                Expanded(
                  child: _buildStatChip('异常数', '${_talkerStats!['exceptions']}', _getStatColor('warning')),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 系统信息
            _buildStatRow('日志提供者', _talkerStats!['provider']),
            _buildStatRow('版本', 'v${_talkerStats!['version']}'),
            _buildStatRow('最大历史条目', '${_talkerStats!['maxHistoryItems']}'),
            
            const SizedBox(height: 16),
            
            // 日志类型分布
            if (_talkerStats!['byType'] != null && _talkerStats!['byType'].isNotEmpty) ...[
              Text(
                '日志类型分布',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 8),
              ..._buildLogTypeStats(),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildLogTypeStats() {
    final byType = _talkerStats!['byType'] as Map<String, dynamic>;
    final List<Widget> widgets = [];
    
    byType.forEach((type, count) {
      widgets.add(
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(type),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '$count',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    });
    
    return widgets;
  }

  Widget _buildQuickActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.build,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  '快速操作',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 打开完整日志界面按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => TalkerScreen(
                        talker: _talkerService.talker,
                        theme: TalkerScreenTheme(
                          backgroundColor: Theme.of(context).colorScheme.surface,
                          textColor: Theme.of(context).colorScheme.onSurface,
                          cardColor: Theme.of(context).colorScheme.surfaceVariant,
                        ),
                      ),
                    ),
                  );
                },
                icon: const Icon(Icons.bug_report),
                label: const Text('打开 Talker 完整日志界面'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 清空日志
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('确认清空'),
                      content: const Text('这将清空所有 Talker 日志历史，包括错误、异常和HTTP请求日志。此操作不可撤销。'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('取消'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            _talkerService.clearHistory();
                            _loadStats();
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(content: Text('日志已清空')),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.error,
                          ),
                          child: const Text('确认清空'),
                        ),
                      ],
                    ),
                  );
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('清空所有日志'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  foregroundColor: Theme.of(context).colorScheme.onError,
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 测试日志
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  _talkerService.info('🎯 测试信息日志');
                  _talkerService.warning('⚠️ 测试警告日志');
                  _talkerService.error('❌ 测试错误日志');
                  _talkerService.logAuth('🔐 测试认证日志');
                  _talkerService.logCache('💾 测试缓存日志');
                  _loadStats();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('已生成测试日志')),
                  );
                },
                icon: const Icon(Icons.science),
                label: const Text('生成测试日志'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Text(
              value,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 16,
              ),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheTab() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCacheSection(),
          const SizedBox(height: 24),
          _buildCacheActionsSection(),
        ],
      ),
    );
  }

  Widget _buildCacheSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.storage,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  '缓存信息',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStatRow('缓存类型', 'dio_cache_interceptor'),
            _buildStatRow('存储方式', 'MemCacheStore (内存)'),
            _buildStatRow('缓存策略', 'CachePolicy.request'),
            _buildStatRow('最大保存时间', '7天'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '缓存功能现在由 dio_cache_interceptor 自动管理，支持 HTTP 缓存指令 (ETag, Last-Modified, Cache-Control 等)',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCacheActionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.build,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  '缓存管理',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('关于缓存'),
                      content: const Text('缓存现在由 dio_cache_interceptor 自动管理，基于 HTTP 标准指令 (Cache-Control, ETag 等) 自动处理缓存的存储和过期。无需手动清理。'),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('了解'),
                        ),
                      ],
                    ),
                  );
                },
                icon: const Icon(Icons.info),
                label: const Text('关于HTTP缓存'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickLogsTab() {
    return TalkerBuilder(
      talker: _talkerService.talker,
      builder: (context, data) {
        if (data.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
                ),
                const SizedBox(height: 16),
                Text(
                  '暂无日志记录',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => TalkerScreen(
                          talker: _talkerService.talker,
                        ),
                      ),
                    );
                  },
                  icon: const Icon(Icons.bug_report),
                  label: const Text('打开完整日志界面'),
                ),
              ],
            ),
          );
        }

        // 显示最近的日志（简化版本）
        final recentLogs = data.take(20).toList();
        return Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '最近日志 (${recentLogs.length}/${data.length})',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => TalkerScreen(
                            talker: _talkerService.talker,
                          ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.open_in_new),
                    label: const Text('查看全部'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                itemCount: recentLogs.length,
                itemBuilder: (context, index) {
                  final log = recentLogs[index];
                  return _buildQuickLogEntry(log);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickLogEntry(TalkerData log) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getLogColor(log).withOpacity(0.2),
          child: Text(
            log.title?.substring(0, 1) ?? '?',
            style: TextStyle(
              color: _getLogColor(log),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getLogColor(log).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getLogColor(log).withOpacity(0.3)),
              ),
                              child: Text(
                  log.title ?? 'Unknown',
                  style: TextStyle(
                    color: _getLogColor(log),
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                log.displayMessage,
                style: const TextStyle(fontSize: 13),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
          ],
        ),
        subtitle: Text(
          log.generateTextMessage().substring(0, 
            log.generateTextMessage().length > 50 ? 50 : log.generateTextMessage().length),
          style: Theme.of(context).textTheme.bodySmall,
          overflow: TextOverflow.ellipsis,
          maxLines: 2,
        ),
        onTap: () {
          // 点击跳转到完整日志界面
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => TalkerScreen(
                talker: _talkerService.talker,
              ),
            ),
          );
        },
      ),
    );
  }

  Color _getLogColor(TalkerData log) {
    final title = log.title?.toLowerCase() ?? '';
    if (title.contains('error')) {
      return _getStatColor('error');
    } else if (title.contains('warning')) {
      return _getStatColor('warning');
    } else if (title.contains('api')) {
      return _getStatColor('primary');
    } else if (title.contains('auth')) {
      return _getStatColor('secondary');
    } else {
      return _getStatColor('info');
    }
  }
}